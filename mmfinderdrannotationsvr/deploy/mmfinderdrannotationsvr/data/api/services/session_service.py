import json
from ..utils.xml_decode_test import ErrorType, get_sessionid_log_v2
from ..utils.batch_simplify_xml import process_trajectory
from api.models.task import (
    GetSessionRequest,
    GetSessionResponse
)

class SessionService:
    @staticmethod
    async def get_operations(request: GetSessionRequest) -> GetSessionResponse:        
        try:
            df = get_sessionid_log_v2(request.app_id,request.user_id,request.start_time,request.end_time)
            # print(df)
            df = df.sort_values(by='timestamp_')
            results = []
            results_index = 0
            scroll_start_indices = []
            scroll_end_items = []
            scroll_end_items_index = 0 
            
            trajectories = df.to_dict('records')    
            for i, trajectory in enumerate(trajectories):
                simplied_dom = process_trajectory(trajectory)
                if simplied_dom.get('clickitem_obj_', {}).get('event') == 'scrollstart':
                    scroll_start_indices.append(results_index)
                if simplied_dom.get('clickitem_obj_', {}).get('event') == 'scrollend':
                    scroll_end_items_index+=1
                    scroll_end_items.append(json.dumps(simplied_dom, ensure_ascii=False, indent=2))
                    continue
                results_index+=1
                results.append(json.dumps(simplied_dom, ensure_ascii=False, indent=2))
            # 顺序修正
            for start_idx in reversed(scroll_start_indices):
                results.insert(start_idx + 1, scroll_end_items[scroll_end_items_index - 1])
                scroll_end_items_index-=1
                
            return GetSessionResponse(
                operations = results if results else []
            )
        except Exception as e:
            print(e)
            return GetSessionResponse(
                operations = []
            )