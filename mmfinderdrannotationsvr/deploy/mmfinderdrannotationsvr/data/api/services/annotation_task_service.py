from typing import List, Optional
from fastapi import HTT<PERSON>Exception
from sqlalchemy import select, update, func, and_
from sqlalchemy.future import select
from api.db.database import get_session
from api.models.task import (
    CreateTaskRequest,
    Task,
    AssignTaskRequest,
    StartTaskRequest,
    FinishTaskRequest,
    DeleteTaskRequest,
    HasPermissionRequest
)
from api.models.annotation_task import AnnotationTask
from api.models.task_status import TaskStatus
from api.utils.rainbow.client import get_rainbow_config

class AnnotationTaskService:
    @staticmethod
    async def create_task(request: CreateTaskRequest) -> int:
        async with get_session() as session:
            task = AnnotationTask(
                app_id=request.app_id,
                app_name=request.app_name,
                query=request.query,
                status=TaskStatus.INITIAL.value
            )
            session.add(task)
            await session.flush()
            return task.id

    @staticmethod
    async def list_tasks(
        assignee: Optional[str] = None,
        app_id: Optional[str] = None,
        status: Optional[str] = None,
        is_deleted: Optional[bool] = None,
        is_backfilled: Optional[bool] = None,
        page: int = 1,
        page_size: int = 10
    ) -> List[Task]:
        async with get_session() as session:
            # 基础查询（用于获取总数）
            count_query = select(func.count()).select_from(AnnotationTask)
            
            # 基础查询（用于获取分页数据）
            data_query = select(AnnotationTask).order_by(AnnotationTask.created_at.desc())
            
            # 添加筛选条件（同时应用到两个查询）
            conditions = []
            if assignee is not None:
                if assignee.strip() in ("null", ""):
                    assignee = None
                conditions.append(AnnotationTask.assignee == assignee)
            if app_id is not None:
                conditions.append(AnnotationTask.app_id == app_id)
            if status is not None:
                conditions.append(AnnotationTask.status == status)
            if is_deleted is not None:
                conditions.append(AnnotationTask.is_deleted == is_deleted)
            if is_backfilled is not None:
                if is_backfilled == True:
                    conditions.append(AnnotationTask.backfilled_annotation_id != None)
                else:
                    conditions.append(AnnotationTask.backfilled_annotation_id == None)
            if conditions:
                count_query = count_query.where(*conditions)
                data_query = data_query.where(*conditions)
            
            # 执行总数查询
            total_result = await session.execute(count_query)
            total = total_result.scalar_one()
            
            # 执行分页查询
            offset = (page - 1) * page_size
            paginated_query = data_query.offset(offset).limit(page_size)
            data_result = await session.execute(paginated_query)
            tasks = data_result.scalars().all()
            
            # 返回任务列表和总记录数
            return {
                "tasks": [Task(**task.to_dict()) for task in tasks], 
                "total": total,
                "page": page,
                "page_size": page_size
            }

    @staticmethod
    async def get_task(task_id: int) -> Task:
        async with get_session() as session:
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                raise HTTPException(status_code=404, detail="Task not found")
            
            return Task(**task.to_dict())

    @staticmethod
    async def assign_tasks(request: AssignTaskRequest) -> bool:
        async with get_session() as session:
            stmt = (
                update(AnnotationTask)
                .where(and_(AnnotationTask.id.in_(request.task_ids), AnnotationTask.status == TaskStatus.INITIAL.value))
                .values(assignee=request.assignee)
            )
            await session.execute(stmt)
            return True

    @staticmethod
    async def start_task(request: StartTaskRequest) -> bool:
        async with get_session() as session:
            # First get the task to check assignee
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == request.task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                raise HTTPException(status_code=404, detail="Task not found")
            
            if task.assignee != request.rtx:
                raise HTTPException(status_code=403, detail="Only the assigned user can start this task")

            stmt = (
                update(AnnotationTask)
                .where(
                    AnnotationTask.id == request.task_id,
                    AnnotationTask.status == TaskStatus.INITIAL.value
                )
                .values(
                    status=TaskStatus.RUNNING.value,
                    start_time=request.start_time
                )
            )
            result = await session.execute(stmt)
            if result.rowcount == 0:
                raise HTTPException(status_code=400, detail="Task cannot be started")
            return True

    @staticmethod
    async def finish_task(request: FinishTaskRequest) -> bool:
        async with get_session() as session:
            # First get the task to check assignee
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == request.task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                raise HTTPException(status_code=404, detail="Task not found")
            
            if task.assignee != request.rtx:
                raise HTTPException(status_code=403, detail="Only the assigned user can finish this task")

            stmt = (
                update(AnnotationTask)
                .where(
                    AnnotationTask.id == request.task_id,
                    AnnotationTask.status == TaskStatus.RUNNING.value
                )
                .values(
                    status=TaskStatus.SUCCESS.value,
                    end_time=request.end_time
                )
            )
            result = await session.execute(stmt)
            if result.rowcount == 0:
                raise HTTPException(status_code=400, detail="Task cannot be finished")
            return True

    @staticmethod
    async def delete_task(request: DeleteTaskRequest) -> bool:
        async with get_session() as session:
            # First check if the task exists
            result = await session.execute(
                select(AnnotationTask).where(AnnotationTask.id == request.task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                raise HTTPException(status_code=404, detail="Task not found")
            
            # Update the is_deleted flag
            stmt = (
                update(AnnotationTask)
                .where(AnnotationTask.id == request.task_id)
                .values(is_deleted=True)
            )
            await session.execute(stmt)
            return True 
        
    @staticmethod
    async def has_permission(request: HasPermissionRequest) -> bool:
        try:
            config = get_rainbow_config()
            admin_members = config.get("admin_members", [])
            if request.rtx in admin_members:
                return True
            else:
                return False
        except Exception as e:
            return False