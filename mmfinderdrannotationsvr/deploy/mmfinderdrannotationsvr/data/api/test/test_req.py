import requests
import json
from datetime import datetime
import os

# 定义API端点
url = "http://localhost:8000/session/get_session_data"

# 准备请求数据（根据GetSessionRequest模型调整）
request_data = {
    "app_id": "wx21c7506e98a2fe75",
    "user_id": "2d172f8a07b572a1458b2adac8669183eff01e85f4f7ba5db7f3302cb00429bf",
    "start_time": "2025-05-27 20:20:00",  
    "end_time": "2025-05-27 20:35:00"     
}
headers = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

# 定义输出目录和文件名
output_dir = "api_responses"
output_filename = f"response_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

try:
    # 发送POST请求
    response = requests.post(
        url,
        data=json.dumps(request_data),
        headers=headers
    )
    
    # 检查响应状态
    if response.status_code == 200:
        # 解析响应数据
        result = response.json()
        
        items =result.get('data').get('operations')
        for ans in items:
            # str to json
            ans = json.loads(ans)
            time_ = ans.get('timestamp_')
            print(f"时间戳: {time_}")
            time_ = datetime.fromtimestamp(time_ / 1000).strftime('%Y-%m-%d %H:%M:%S')
            st = ans.get('starttime_')
            st = datetime.fromtimestamp(st).strftime('%Y-%m-%d %H:%M:%S')
            et = ans.get('endtime_')
            et = datetime.fromtimestamp(et).strftime('%Y-%m-%d %H:%M:%S')
            print(f"时间戳: {time_}",'start',st,'end',et,)
            print('sessionid_',ans.get('sessionid_'))
            print(ans.get('clickitem_obj_').get('event'))
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        def parse_nested_json(data):
            """递归解析嵌套的JSON字符串"""
            if isinstance(data, dict):
                return {k: parse_nested_json(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [parse_nested_json(v) for v in data]
            elif isinstance(data, str):
                try:
                    # 尝试解析字符串是否为JSON
                    parsed = json.loads(data)
                    return parse_nested_json(parsed)
                except json.JSONDecodeError:
                    return data
            else:
                return data

        # 解析嵌套的JSON字符串
        parsed_result = parse_nested_json(result)
        
        # 写入格式化的JSON文件
        output_path = os.path.join(output_dir, output_filename)
        with open(output_path, 'w', encoding='utf-8') as f:
            # 使用更易读的格式：缩进4个空格，按key排序，确保中文正常显示
            json.dump(
                parsed_result, 
                f, 
                indent=4,               # 增加缩进
                ensure_ascii=False,     # 确保中文正常显示
                sort_keys=True,         # 按键排序
                separators=(',', ': ')  # 美化分隔符
            )
        print(f"结果已保存至: {output_path}")
        print(f"文件大小: {os.path.getsize(output_path)/1024:.2f} KB")
    else:
        print(f"请求失败，状态码：{response.status_code}")
        print(f"错误信息：{response.text}")
        
except Exception as e:
    print(f"请求过程中发生异常：{str(e)}")
