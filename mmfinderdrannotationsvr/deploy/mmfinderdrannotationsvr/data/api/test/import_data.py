# only for IDC
import requests
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict

def create_single_task(api_url: str, task_data: Dict) -> Dict:
    """
    单个任务创建函数（线程安全）
    
    参数:
        api_url: 接口地址
        task_data: 单个任务数据
        
    返回:
        包含任务创建结果的字典
    """
    try:
        response = requests.post(
            api_url,
            headers={"Content-Type": "application/json"},
            data=json.dumps(task_data),
            timeout=10  # 添加超时设置
        )
        response.raise_for_status()
        return {
            "success": True,
            "task_id": response.json().get("task_id"),
            "raw_response": response.json(),
            "task_data": task_data
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "task_data": task_data
        }

def batch_create_tasks_concurrent(api_url: str, tasks_data: List[Dict], max_workers: int = 8) -> List[Dict]:
    """
    多线程批量创建标注任务
    
    参数:
        api_url: 接口地址
        tasks_data: 任务数据列表
        max_workers: 线程数 (默认为5)
        
    返回:
        包含所有任务结果的列表
    """
    results = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务到线程池
        future_to_task = {
            executor.submit(create_single_task, api_url, task): task
            for task in tasks_data
        }
        
        # 获取完成的任务结果
        for future in as_completed(future_to_task):
            task_data = future_to_task[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                results.append({
                    "success": False,
                    "error": str(e),
                    "task_data": task_data
                })
    
    return results

def load_tasks_from_jsonl(file_path: str) -> list:
    """
    从JSONL文件中读取任务数据
    
    参数:
        file_path: JSONL文件路径
        
    返回:
        由app_id、app_name和query组成的任务列表
    """
    tasks = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                # 解析每行JSON数据
                data = json.loads(line.strip())
                
                # 提取所需字段组成任务
                task = {
                    "app_id": data["appid"],  # 注意字段名可能是appid或app_id
                    "app_name": data["app_name"],
                    "query": data["query"]
                }
                tasks.append(task)
                
            except json.JSONDecodeError:
                print(f"警告: 跳过无法解析的行: {line}")
            except KeyError as e:
                print(f"警告: 行缺少必要字段 {e}: {line}")
    
    return tasks
# 使用示例
if __name__ == "__main__":
    # 接口地址 (请替换为实际地址)
    API_URL = "http://mmfinderdrannotationsvr.polaris:8080/annotations/tasks"
    
    # 准备批量任务数据
    tasks_to_create = load_tasks_from_jsonl("./hxj_14_18_instruct_and_screen_v2_13APP_addq_filter_valid2_sorted_bert_filtered.jsonl")
    
    # 执行并发批量创建 (5线程)
    print("开始并发创建任务...")
    creation_results = batch_create_tasks_concurrent(API_URL, tasks_to_create)
    
    # 统计结果
    success_count = sum(1 for r in creation_results if r["success"])
    print(f"\n任务完成: 成功 {success_count}/{len(creation_results)}")
    
    # 打印详细结果 (可选)
    print("\n详细结果:")
    for result in creation_results:
        status = "成功" if result["success"] else "失败"
        task_data = result["task_data"]
        print(f"应用 {task_data['app_id']} - {status}", end="")
        if result["success"]:
            print(f" (任务ID: {result['task_id']})")
        else:
            print(f" (错误: {result['error']})")
