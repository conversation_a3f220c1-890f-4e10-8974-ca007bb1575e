from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from ..models.response import StandardResponse, ErrorCode

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=StandardResponse(
            code=ErrorCode.VALIDATION_ERROR,
            msg=str(exc),
            data=None
        ).dict()
    )

async def http_exception_handler(request: Request, exc):
    if exc.status_code == status.HTTP_404_NOT_FOUND:
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=StandardResponse(
                code=ErrorCode.NOT_FOUND,
                msg=str(exc.detail),
                data=None
            ).dict()
        )
    elif exc.status_code == status.HTTP_403_FORBIDDEN:
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=StandardResponse(
                code=ErrorCode.PERMISSION_DENIED,
                msg=str(exc.detail),
                data=None
            ).dict()
        )
    elif exc.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=StandardResponse(
                code=ErrorCode.SYSTEM_ERROR,
                msg="Internal Server Error",
                data=None
            ).dict()
        )
    else:
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=StandardResponse(
                code=ErrorCode.BUSINESS_ERROR,
                msg=str(exc.detail),
                data=None
            ).dict()
        )

async def general_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=StandardResponse(
            code=ErrorCode.SYSTEM_ERROR,
            msg=str(exc),
            data=None
        ).dict()
    ) 