from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from contextlib import asynccontextmanager
from api.models.annotation_task import Base

# Will be initialized in app startup
engine = None
async_session_maker = None

async def init_db(database_url: str):
    """Initialize database connection"""
    global engine, async_session_maker
    
    # Create async engine
    engine = create_async_engine(
        database_url,
        echo=False,  # Set to True for SQL logging
        pool_pre_ping=True,  # Enable connection pool pre-ping
        pool_size=5,  # Set pool size
        max_overflow=10  # Set max overflow
    )
    
    # Create async session maker
    async_session_maker = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )

    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

async def close_db():
    """Close database connection"""
    global engine
    if engine:
        await engine.dispose()

@asynccontextmanager
async def get_session() -> AsyncSession:
    """Get a database session"""
    if not async_session_maker:
        raise RuntimeError("Database not initialized")
    
    async with async_session_maker() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise 