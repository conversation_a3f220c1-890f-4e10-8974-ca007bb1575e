from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
import pytz

class CreateTaskRequest(BaseModel):
    app_id: str
    app_name: str
    query: str

class CreateTaskResponse(BaseModel):
    task_id: int
    success: bool = True

class Task(BaseModel):
    task_id: int
    app_id: str
    app_name: str
    query: str
    status: str
    is_deleted: bool
    backfilled_annotation_id: Optional[int] = None
    assignee: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        json_encoders = {
            datetime: lambda dt: dt.replace(tzinfo=pytz.UTC).astimezone(pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S') if dt else None
        }

class AssignTaskRequest(BaseModel):
    task_ids: List[int]
    rtx: str
    assignee: str

class StartTaskRequest(BaseModel):
    task_id: int
    rtx: str
    start_time: datetime

class FinishTaskRequest(BaseModel):
    task_id: int
    rtx: str
    end_time: datetime

class DeleteTaskRequest(BaseModel):
    task_id: int

class ResetTaskRequest(BaseModel):
    task_id: int

class HasPermissionRequest(BaseModel):
    rtx: str

class HasPermissionResponse(BaseModel):
    has_permission: bool
    success: bool = True

class PaginatedTasksResponse(BaseModel):
    tasks: List[Task]
    total: int
    page: int
    page_size: int
    
class SuccessResponse(BaseModel):
    success: bool 
    
class GetSessionRequest(BaseModel):
    app_id: str
    user_id: str
    start_time: datetime
    end_time: datetime
class GetSessionResponse(BaseModel):
    operations: Optional[List] = []