from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON>an, DateTime
from sqlalchemy.ext.declarative import declarative_base
from api.models.task_status import TaskStatus
from api.utils.time import get_shanghai_time

Base = declarative_base()

class AnnotationTask(Base):
    __tablename__ = 'ai_miniprogram_annotation_tasks'

    id = Column(Integer, primary_key=True)
    app_id = Column(String, nullable=False)
    app_name = Column(String, nullable=False)
    query = Column(String, nullable=False)
    status = Column(String, nullable=False, default=TaskStatus.INITIAL.value)
    is_deleted = Column(Boolean, default=False)
    assignee = Column(String)
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    created_at = Column(DateTime, default=get_shanghai_time)
    updated_at = Column(DateTime, default=get_shanghai_time, onupdate=get_shanghai_time)
    backfilled_annotation_id = Column(Integer, nullable=True)
    
    def to_dict(self):  
        return {
            'task_id': self.id,
            'app_id': self.app_id,
            'app_name': self.app_name,
            'query': self.query,
            'status': self.status,
            'is_deleted': self.is_deleted,
            'assignee': self.assignee,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'backfilled_annotation_id': self.backfilled_annotation_id
        } 