import pandas as pd
import json
from datetime import date
from collections import Counter
import os
import argparse
from .batch_simplify_xml import process_trajectory
from .utils import replace_consecutive_stars

def convert_to_json_serializable(item):
    if isinstance(item, (date, pd.Timestamp)):
        return str(item)
    elif isinstance(item, dict):
        return {key: convert_to_json_serializable(value) for key, value in item.items()}
    elif isinstance(item, list):
        return [convert_to_json_serializable(sub_item) for sub_item in item]
    return item

appid2first_page_path = {
    'wxcf62686dc9d61f90': 'pages/index/index.html',
    'wx729238547ac7a14c': 'modules/home/<USER>',
    'wx56dbc76c7b340f86': 'pages/index/index.html',
    'wx366ae41607c228d1': 'pages/index/index.html',
    'wxc1d4e8f52a93da7e': 'pages/index/index.html',
    'wxad3150031786d672': 'pages/main.html', # 腾讯手机充值
    'wx4ffb369b6881ee5e': 'pages/index/index.html', # 腾讯自选股
    'wxc30ae3bc7fb4cab1': 'pages/index/index.html', # 墨迹天气
    'wxe671ce744efccefd': None, # 今日金价
    'wx565957244cb7d81f': 'pages/index/index.html', # 汇率即时查询
    'wx21c7506e98a2fe75': 'pages/index/home.html', # 瑞幸
    'wx734c1ad7b3562129': 'pages/home/<USER>', # 大众
    'wx3dcca19d0aa51755': 'customized/pages/main/index.html',
    'wx8a5d6f9fad07544e': 'pages/index/main.html',
}

def jsonfy(value):
    if isinstance(value, dict) or isinstance(value, list):
        return value
    if isinstance(value, str):
        return json.loads(value)
    raise Exception(f'Unknown type: {type(value)}')


# filter: 长度, back事件, clickitem_为空, 非app的正常起始页
# filter(need update): 不以小程序的默认首页作为会话的开始
# filter(need update): 多次进入相同的实例页面(要考虑加购之后、完整支付之后进入同一个页面)
# filter_candidates: 点击左上角的回退(通过'点击pos在左上角的元素'&'去到一个之前有的页面做判断')
from collections import defaultdict
error_cnt = defaultdict(int)
def is_trajectory_valid(trajectory):
    appid = trajectory[0]['appid_']
    first_page_path = appid2first_page_path[appid] if appid in appid2first_page_path else None
    traveled_pages_paths = []
    for item_idx, item in enumerate(trajectory):
        path = item['path_']
        if len(traveled_pages_paths) == 0 or path != traveled_pages_paths[-1]:
            traveled_pages_paths.append(path)
        if item_idx == 0 and first_page_path is not None and path != first_page_path:
            error_cnt["first_page_not_match"] += 1
            return False
        sessionid = item['sessionid_']
        clickitem = item['clickitem_']
        if clickitem == '':
            error_cnt["clickitem_empty"] += 1
            return False
        try:
            clickitem_event = jsonfy(replace_consecutive_stars(clickitem))
        except Exception as e:  # TODO: 临时逻辑, 下个版本要去掉
            error_cnt["jsonfy_error"] += 1
            print(f'jsonfy_error: {clickitem}')
            return False
        if clickitem_event == 'back':
            error_cnt["back_event"] += 1
            return False
        elif clickitem_event == 'unimplemented':
            error_cnt["unimplemented_event"] += 1
            return False
        # if item['label_index_'] < 0:
        #     error_cnt["label_index_error"] += 1
        #     return False
    if len(traveled_pages_paths) - len(set(traveled_pages_paths)) > 2:
        error_cnt["maybe_back_event"] += 1
        # print(f'\n\nmaybe_back_event: ')
        # for path in traveled_pages_paths:
        #     print(path)
        return False
    return True

def is_simplified_dom_valid(simplified_doms):
    for simplified_dom in simplified_doms:
        if simplified_dom['label_index'] < 0 and simplified_dom['clickitem_obj_']['event'] in ['click', 'tap']:
            error_cnt["label_index_error"] += 1
            return False
        if simplified_dom['clickitem_obj_']['event'] in ['back']:
            error_cnt["xdom_back_event"] += 1
            return False
        if simplified_dom['clickitem_obj_']['event'] in ['unimplemented']:
            error_cnt["xdom_unimplemented_event"] += 1
            return False
        if simplified_dom['clickitem_obj_']['event'] in ['home']:
            error_cnt["xdom_home_event"] += 1
            return False
        if simplified_dom['clickitem_obj_']['event'] in ['pancancel']:
            error_cnt["xdom_pancancel_event"] += 1
            return False
        # if simplified_dom['clickitem_obj_']['event'] in ['panstart', 'panend']:
        #     error_cnt["xdom_pan_event"] += 1
        #     return False
    return True

event_cnt = defaultdict(int)
def get_user_operation_data(df, output_json_path, args):
    fout = open(output_json_path, 'a')

    # 按 sessionid_ 分组，并对每个组按 time 排序
    grouped = df.groupby('short_sessionid_').apply(lambda x: x.sort_values(by='timestamp_')).reset_index(drop=True)

    # 创建一个字典，键为 sessionid_  对应的排序后的记录列表
    result_dict = {}
    for name, group in grouped.groupby('short_sessionid_'):
        result_dict[name] = group.to_dict(orient='records')

    # 输出结果
    length_dist = Counter()
    last_page_instance_id = None
    for key, value in result_dict.items():
        value = convert_to_json_serializable(value)
        length = len(value)
        if  not(args.trajectory_length_min <= length <= args.trajectory_length_max):
            continue
        simplied_doms = [process_trajectory(item) for item in value]
        for sub_trajectory, simplied_dom in zip(value, simplied_doms):
            sub_trajectory['label_index'] = simplied_dom['label_index']
            sub_trajectory['event'] = simplied_dom['clickitem_obj_']['event']
            sub_trajectory['is_new_page'] = sub_trajectory['page_instance_id_'] != last_page_instance_id
            last_page_instance_id = sub_trajectory['page_instance_id_']
        if not is_trajectory_valid(value) or not is_simplified_dom_valid(simplied_doms):
            continue
        for sub_trajectory in value:
            event_cnt[sub_trajectory['event']] += 1
        fout.write(key + "\t*#&\t" + json.dumps(value, ensure_ascii=False) + '\n')
        length_dist[length] += 1

    print("total length is ", sum(length_dist.values()))
    print("error_cnt is ", error_cnt)
    print("event_cnt is ", event_cnt)
    for i in range(len(length_dist)):
        print(i, " ",  length_dist[i])

def get_session_id(s):
    #hash=802408136&ts=1743774812155&host=&version=671103404&device=2#109385745#1743774845601
    return s.strip().split("#")[0]

def get_page_instance_id(s):
    #hash=3193143403&ts=1747727900521&host=&version=671103598&device=2#168833998#1747728157509#CGI5
    return s.strip().split("#")[1]

def get_timestamp(s):
    return int(s.strip().split("#")[-2])


def group_by_session_id(args):
    pickle_file_path = args.input_pickle_dir
    output_json_path = args.output_json_path

    target_pickles_str = args.target_pickles
    target_pickles = []
    if target_pickles_str == "all":
        target_pickles = [f for f in os.listdir(pickle_file_path) if f.endswith("pickle")]
    else:
        target_pickles = target_pickles_str.strip().split(",")

    print("target_pickles is ", target_pickles)
    # pickle数据group by 之后保存成json格式
    file_list = [os.path.join(pickle_file_path, f) for f in target_pickles]
    for file_path in file_list:
        print("Processing ... ", file_path)
        df = pd.read_pickle(file_path)
        # print("DataFrame columns:", df.columns)
        # 查看数据基本信息
        # print("数据基本信息：")
        # df.info()

        # 查看数据集行数和列数
        # rows, columns = df.shape
        # print(rows, columns)
        # print("前几行信息：")
        #print(df.head().to_csv(sep='\t', na_rep='nan'))
        # print(df.head())
        # cnt = 0
        # for index, row in df.iterrows():
        #     print(row['sessionid_'])
        #     print(f"Index: {index}, appid_: {row['appid_']}, sessionid_: {row['sessionid_']}, clickitem_: {row['clickitem_']}")
        #     cnt += 1
        #     if cnt > 10: break
        # 提取sessionid_
        df['short_sessionid_'] = df['sessionid_'].apply(get_session_id)
        df['timestamp_'] = df['sessionid_'].apply(get_timestamp)
        df['page_instance_id_'] = df['sessionid_'].apply(get_page_instance_id)

        get_user_operation_data(df, output_json_path, args)


def split_txt_file(args):
    num_parts = args.split_num
    file_path = args.output_json_path
    try:
        # 读取源文件的所有行
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # 计算总行数
        total_lines = len(lines)

        # 计算每个小文件应包含的行数
        lines_per_part = total_lines // num_parts

        # 分割文件
        for i in range(num_parts):
            start_index = i * lines_per_part
            end_index = start_index + lines_per_part if i < num_parts - 1 else total_lines

            # 确定小文件的文件名
            output_file_path = os.path.join(args.split_target_dir, f'part_{i + 1}.txt')

            # 写入小文件
            with open(output_file_path, 'w', encoding='utf-8') as output_file:
                output_file.writelines(lines[start_index:end_index])

        print(f'文件已成功分割成 {num_parts} 个小文件。')
    except FileNotFoundError:
        print(f"错误：未找到文件 {file_path}。")
    except Exception as e:
        print(f"发生未知错误：{e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="MiniP data Group by session")
    parser.add_argument("--input_pickle_dir", type=str, help="input_pickle_dir", default="")
    parser.add_argument("--target_pickles", type=str, help="all|file1,file2,file3",
                        default="all")

    parser.add_argument("--trajectory_length_min", type=int, help="trajectory_length_min",
                        default=3)
    parser.add_argument("--trajectory_length_max", type=int, help="trajectory_length_max",
                        default=20)
    parser.add_argument("--output_json_path", type=str, help="output_json_path",
                        default="")

    parser.add_argument("--split_num", type=int, help="split_num",
                        default=10)
    parser.add_argument("--split_target_dir", type=str, help="split_target_dir",
                        default="")
    parser.add_argument("--function", type=str, help="function",
                        default="group_by_session_id")


    print("Start....")
    args = parser.parse_args()
    print("args is : ")
    print(args)

    if args.function == "group_by_session_id":
        if os.path.exists(args.output_json_path):
            print(f"{args.output_json_path} has been exists, please make sure")
            exit(0)
        group_by_session_id(args)
    elif args.function == "split_txt_file":
        if os.path.exists(args.split_target_dir):
            print(f"{args.split_target_dir} has been exists, please make sure")
            exit(0)
        os.makedirs(args.split_target_dir)
        split_txt_file(args)

