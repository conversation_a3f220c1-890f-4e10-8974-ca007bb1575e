# -*- coding: utf-8 -*-
import time
import json
from rainbow_cpplib.rainbow_client import RainbowClient

g_rc = None

def init_rainbow():
    init_param = {
      "connectStr":"http://api.rainbow.woa.com:8080", #访问地址：【北极星】或者【域名】
      "isUsingPolaris":False, #是否使用北极星访问方式，True: connectStr需要填北极星地址，False：域名地址
      "fileCachePath":"./", #缓存文件目录
      "tokenConfig": {
        "isOpenSign": True,
        "app_id": "b315e2e5-40b2-4ac0-b724-a61fd2c2cfa3",
        "user_id": "b5d30ad81e214dc78e929743152223fa",
        "secret_key": "5a3aca831cec848cfe605db50fe4b19e49e2",
      },
    }
    #初始化，建议使用全局变量，禁止使用RainbowClient(init_param，False)的初始化对象保持在局部变量中（局部对象会导致watch机制失效，add_listener无效）
    #bad_case请查看文件test_rainbow_bad_case.py
    global g_rc
    g_rc=RainbowClient(init_param)

#定义回调函数
def my_callback(peizhi_dict):
    try:
        print("\n\n\n\n----------Called back with  my_callback----------\n")
        print(type(peizhi_dict))
        print(peizhi_dict)
        print("----------Called back with  my_callback end----------\n")
    except Exception as e:
        print("Exception:",e)

init_rainbow()
#定义监听的group,同步服务器配置（服务器中配置变化会自动同步下发）; 若不监听，后续获取的配置都是本地缓存的配置(控制台发布的配置，不会同步)
g_rc.add_listener(group="mini_program_annotation",env_name = "Default",callback=None) #不带回调函数
#g_rc.add_listener(group="mini_program_annotation",env_name = "Default",callback=my_callback) #增加回调函数

def get_rainbow_config():
    """获取七彩石配置"""
    print("\n===========mini_program_annotation============")
    try:
      peizhi_dict = g_rc.get_configs(group = "mini_program_annotation",env_name = "Default")
      key_values = peizhi_dict['data'][0]['key_values']
      parsed_data = {}

      for item in key_values:
          key = item['key']
          value_str = item['value']

          if key == 'admin_members':
              try:
                  # Parse the JSON string into a Python list
                  parsed_value = json.loads(value_str)
                  parsed_data[key] = parsed_value
              except json.JSONDecodeError as e:
                  print(f"Error decoding JSON for key '{key}': {e}")
                  parsed_data[key] = value_str # Keep original if parsing fails
          else:
              parsed_data[key] = value_str

      print(parsed_data)
      return parsed_data
    except Exception as e:
      print("Exception:",e)
      return {'admin_members': ['huaideliu', 'dreamshan', 'ronnyhe', 'jinweiguo', 'susiejsun', 'markrocwang', 'mianbaoxie']}
    

if __name__ == "__main__":
  while True:
      peizhi_dict = g_rc.get_configs(group = "mini_program_annotation",env_name = "Default")
      print("\n===========mini_program_annotation============")
      print(peizhi_dict)
      data = peizhi_dict['data'][0]['key_values']
      if data and isinstance(data, list) and isinstance(data[0], dict):
          json_string = data[0].get('value')
          if json_string:
              try:
                  name_list = json.loads(json_string)
                  print(name_list)
              except json.JSONDecodeError as e:
                  print(f"Error decoding JSON: {e}")
          else:
              print("No 'value' key found in the dictionary.")
      else:
          print("Input data is not in the expected format.")
      time.sleep(10)