from .element_utils import *
import xml.etree.ElementTree as ET
from itertools import combinations
import sys
import os
from pathlib import Path
from .convert_wechat_minihtml_to_xml import convert as wxconvert
# from convert import convert as wxconvert
import json
from .utils import replace_consecutive_stars
def process_accessibility_tree(root):
    reduced_elements = []
    def dfs_tree(node, parent=None, node_order="", dep=0):
        if "package" in node.attrib and node.attrib["package"].startswith("com.android.systemui"):
            return []
        if "resource-id" in node.attrib and node.attrib["resource-id"].startswith("com.google.android.inputmethod.latin:id"):
            return []

        sequ_subtree = []
        if len(node) == 0:
            leaf_view = make_view(node)
            leaf_view.update({"id": node_order})
            sequ_subtree.append(leaf_view)

        for id, child in enumerate(node):
            sub = dfs_tree(child, node, node_order + f"{id},", dep + 1)
            for view in sub:
                sequ_subtree.append(view)

        if is_crucial_node(node) and len(sequ_subtree) > 0:
            node_view = make_element("", "", node, sequ_subtree)
            node_view.update({"id": node_order})
            reduced_elements.append(node_view)
            sequ_subtree = []

        return sequ_subtree

    node_ungrouped = dfs_tree(root)
    return node_ungrouped, reduced_elements

def parameterize_actions(elements, root):   
    last_pos = 0
    output = []
    action_nodes = {el["id"]:idx for idx, el in enumerate(elements)}
    
    target_elements = elements
    def dfs1(node, node_order=""):
        if illegal_node(node):
            return 
        
        if node_order in action_nodes:
            return 
        
        if not gettext(node) == ("", ""):
            node_view = {"id": node_order}
            target_elements.append(node_view)
            
        for id, child in enumerate(node):
            dfs1(child, node_order + f"{id},")
    dfs1(root)
    
    lca_set = set(el["id"] for el in target_elements)
    for el1, el2 in combinations(target_elements, 2):
        lca = get_lca(el1, el2)
        if not lca == "":
            lca_set.add(lca)

    xpath_map = {}
    
    def dfs2(node, node_order="", indent=""):
        if illegal_node(node):
            return
        
        if node_order in action_nodes:
            index = action_nodes[node_order]
            output.append(indent + f"[{last_pos + index}] " + to_str(elements[last_pos + index]))
            indent += "    "
            xpath_map[index] = node.attrib["xpath"]
            
        elif node_order in lca_set:
            node_view = make_view(node)
            node_view["actions"] = set()
            output.append(indent + to_str(node_view))
            indent += "    "
            
        for id, child in enumerate(node):
            dfs2(child, node_order + f"{id},", indent)
    dfs2(root)
    
    
    return "\n".join(output), xpath_map

from collections import defaultdict
error_cnt = defaultdict(int)
def process_trajectory(trajectory, print_flag=False):
    page_session_id = trajectory["sessionid_"]
    raw_html = trajectory["decoded"] #input xml
    if print_flag:
        print("raw_html:",raw_html)
    # 下面这几行, screen xpath index_to_xpath 都是测试中心给的代码, 原则上不需要改变
    xml = wxconvert(raw_html)
    root = ET.fromstring(xml)
    _, elements = process_accessibility_tree(root)
    screen, xpath = parameterize_actions(elements, root)
    index_to_xpath = {path:idx for idx, path in xpath.items()}
    if print_flag:
        print("screen:",screen)
        print("xpath:",xpath)
        print("index_to_xpath:",index_to_xpath)              
    
    if isinstance(trajectory["clickitem_"], str):
        if len(trajectory["clickitem_"].strip()) > 0:
            operate_path = trajectory["clickitem_"].replace(" ", "").replace("\n", "")
            operate_path = replace_consecutive_stars(operate_path)  # TODO: 临时逻辑, 下个版本要去掉
            try:
                operate_path_info = json.loads(operate_path)
            except:
                raise Exception(f'operate_path error: {operate_path}')
        else:
            if "</tab-bar></native>" in raw_html:
                operate_path_info = {"event": "unimplemented"}
            else:
                raise Exception(f'clickitem_ error: {trajectory["clickitem_"]}')
    elif isinstance(trajectory["clickitem_"], dict):
        operate_path_info = trajectory["clickitem_"]
    else:
        raise ValueError("Unknown clickitem_ type")
    if print_flag:
        print("operate_path:",operate_path)
        print("operate_path_info:",operate_path_info)
    # if operate_path_info.get("event", "") == "click":
    #     xml_path = operate_path_info.get("parent", "") # label xpath
    # elif operate_path_info.get("event", "") == "blur":
    #     xml_path = operate_path_info.get("xpath", "") # label xpath
    # elif operate_path_info.get("event", "") == "tap":
    #     xml_path = operate_path_info.get("parent", "") # label xpath
    # elif operate_path_info.get("event", "") == "focus":
    #     xml_path = operate_path_info.get("xpath", "") # label xpath
    # else:
    #     assert False, "Unknown event"
        
    # xml_path = '/' + xml_path
    # if print:
    #     print("xml_path:",xml_path)
    # index = index_to_xpath.get(xml_path,-1)
    # if index == -1:
    #     xpath = operate_path_info.get("xpath", "") # label xpath
    #     new_xml_path = xml_path + xpath
    #     index = index_to_xpath.get(new_xml_path,-2)  # TODO: 这里不要求完全匹配, 前缀匹配且只有一个匹配的时候就行

    assert operate_path_info.get("event", "") in ["click", "blur", "tap", "focus", 'panstart', 'panend', 'back', 'unimplemented', 'home', 'pancancel', 'scrollstart', 'scrollend','touchstart','touchend'], f"Unknown event {operate_path_info.get('event', '')}"
    parent_xml_path = operate_path_info.get("parent", "")
    xml_path = parent_xml_path + operate_path_info.get("xpath", "")
    if not parent_xml_path.startswith("/"):
        parent_xml_path = '/' + parent_xml_path
    if not xml_path.startswith("/"):
        xml_path = '/' + xml_path
    if print_flag:
        print("parent_xml_path:",parent_xml_path)
        print("xml_path:",xml_path)
    index = index_to_xpath.get(xml_path, -1)
    if index == -1:
        index = index_to_xpath.get(parent_xml_path, -1)
    if index == -1:
        error_cnt["no_xpath"] += 1
    else:
        error_cnt["success"] += 1
    
    if print_flag:
        print("index:",index)
    new_trajectories_dict = trajectory
    new_trajectories_dict["label_index"] = index
    new_trajectories_dict["input_xpath"] = xpath
    new_trajectories_dict["screen"] = screen
    new_trajectories_dict["clickitem_obj_"] = operate_path_info
    # new_trajectories_list.append(new_trajectories_dict) 
    #process_data_list.append({"sessionid": sessionid, "page_session_id": page_session_id, "page_xml": page_xml, "click_item":click_item})

    return new_trajectories_dict

def get_step_data(file_path, target_session_ids_path, output_path):
    target_session_ids = set()
    if target_session_ids_path is not None:
        for line in open(target_session_ids_path):
            line_info = json.loads(line.strip())
            sessionid = line_info["sessionid"]
            target_session_ids.add(sessionid.strip())
        print("target_session_ids size is ", len(target_session_ids))

    process_data_list = []
    for line in open(file_path):
        line_info = line.strip().split("\t*#&\t")
        if len(line_info) != 2:
            print("line_info error ", line_info)
        sessionid, trajectories_str = line_info
        # print("sessionid:",sessionid)
        # print("trajectories_str:",trajectories_str)
        if target_session_ids_path is not None and sessionid not in target_session_ids:
            continue
        try:
            trajectories = json.loads(trajectories_str)
        except json.JSONDecodeError as e:
            print(f"JSON 解析错误: {e}")
            print(f"错误位置: {e.pos}")
            print(f"错误行: {e.lineno}")
            print(f"错误列: {e.colno}")
            continue
        length = len(trajectories)
        if length < 1: continue
       
 
        time_stamp = trajectories[0]["timestamp_"]
        app_id = trajectories[0]["appid_"]

        new_trajectories_list = []

        for i, trajectory in enumerate(trajectories):
            # try:
            #     new_trajectories_dict = process_trajectory(trajectory)
            #     new_trajectories_list.append(new_trajectories_dict)
            # except:
            #     continue
            new_trajectories_dict = process_trajectory(trajectory)
            new_trajectories_list.append(new_trajectories_dict)
        new_trajectories_str = json.dumps(new_trajectories_list,ensure_ascii=False)
        # print("new_trajectories_str:",new_trajectories_str)
        # print("trajectories_str:",trajectories_str) 
        with open(output_path, "a", encoding="utf-8") as output_file:
            result = "\t*#&\t".join([sessionid,new_trajectories_str])
            output_file.write(result+'\n')
    return 0

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description=".")

    parser.add_argument("--target_session_ids_path", type=str, help="target_session_ids_path",
                        default=None)
    parser.add_argument("--grouped_json_data_dir_path", type=str, help="grouped_json_data_dir_path")
    parser.add_argument("--output_path", type=str, help="output_path",
                        default="")

    args = parser.parse_args()
    
    target_session_ids_path=args.target_session_ids_path
    grouped_json_data_dir_path=args.grouped_json_data_dir_path
    if args.target_session_ids_path is not None:
        input("Are u sure to use target_session_ids_path to verify the data? Enter to continue...")

    file_list = [os.path.join(grouped_json_data_dir_path, f) for f in os.listdir(grouped_json_data_dir_path)]
    for file_path in file_list:
        print("Processing ", file_path)
        get_step_data(file_path, target_session_ids_path, args.output_path)
    print("error_cnt:",error_cnt)
        
