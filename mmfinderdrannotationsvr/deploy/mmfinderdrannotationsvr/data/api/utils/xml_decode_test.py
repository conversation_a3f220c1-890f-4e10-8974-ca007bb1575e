from pathlib import Path
import pandas as pd
import os
#os.system('pip3 install rawins --upgrade -i https://mirrors.tencent.com/repository/pypi/tencent_pypi/simple --extra-index-url https://mirrors.tencent.com/pypi/simple/')
from rawins import Rawins
import gzip
import pandas as pd
from io import BytesIO
import base64
import csv, json, sys, requests
from io import String<PERSON>
from typing import List, Dict, Any
from Crypto.Cipher import AES
from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA
from .utils import load_private_key_from_base64, decrypt_with_rsa, decode_with_pycryptodome
from tqdm import tqdm
import json
from enum import Enum, auto
from ..utils.group_by_session_id import get_session_id, get_timestamp
# db_conf
mysql_conf = {
    "user": "root",
    "passwd": "9g@gmZh7Hf",
    "port": 3306,
    "host": "***********",
    "db": "mmfd_zhaohui",
    "charset": 'utf8'
}

# # clickhouse conf
ch_conf = {
"host":'**************',
"port": 9000,
"user": 'longvideo_luban',
"password": 'GZqtkM9yG4N6ZKbYsa7r',
"database": 'default' ,
"send_receive_timeout": 120
}

# root conf
# ch_conf = {
#     "host":'**************',
#     "port": 9000,
#     "user": 'default',
#     "password": 'Videochannel@2021',
#     "database": 'default' ,
#     "send_receive_timeout": 120
# }
rawins = Rawins(ch_conf, mysql_conf)
rawins.register("marcusdai")
#3192109930,
#where toUInt32(uin_) in (3193143403)
# where sessionid_ in ('hash=3192109930&ts=1743688369633&host=&version=788529166&device=2#194647208#1743688643032#Tummy')
# sql = """
# select count(), toHour(hour_) from dw_luban.log_34629  where toDate(hour_) = '2025-04-04' group by toHour(hour_) order by  toHour(hour_) desc   
# """


pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option("display.max_colwidth", None)
# print(df[['second_', 'appid_', 'sessionid_', 'length_', 'nativeinfo_','key_']])

private_key = load_private_key_from_base64()

def reverse_kotlin_operation(processed_str: str) -> str:
    print('len:', len(processed_str))
    try:
        compressed_bytes = base64.b64decode(processed_str)
    except Exception as e:
        print("Base64解码失败") 
        return None
    try:
        with gzip.GzipFile(fileobj=BytesIO(compressed_bytes)) as f:
            return f.read().decode('utf-8')
    except gzip.BadGzipFile as e:
        print("GZIP解压失败")
        return None



def process_dataframe(df: pd.DataFrame) -> pd.DataFrame:
    # 1. 拼接所有列（处理可能的空值）
    df['combined'] = df.apply(
        lambda row: ''.join([
            str(row[f'xml{i}_']) 
            for i in range(1, 7) 
            if pd.notna(row[f'xml{i}_']) and str(row[f'xml{i}_']) != ''
        ]),
        axis=1
    )

    df['secret_key'] = df['key_'].apply(lambda x: decrypt_with_rsa(private_key, x))
    # 2. 逆向操作
    df['combined'] = df.apply(lambda row: decode_with_pycryptodome(row['combined'], row['secret_key']),axis=1)

    df['clickitem_'] = df.apply(lambda row: decode_with_pycryptodome(row['clickitem_'], row['secret_key']),axis=1)

    # def process_row(row):
    #     try:
    #         # 调用解密函数
    #         return decode_with_pycryptodome(row['nativeinfo_'], row['secret_key'])
    #     except Exception as e:
    #         # 捕获异常并记录报错的行信息
    #         print('####',row)
    #         return None  # 返回 NaN 或其他占位值

    # df['nativeinfo_'] = df.apply(process_row, axis=1)
    df['nativeinfo_'] = df.apply(lambda row: decode_with_pycryptodome(row['nativeinfo_'], row['secret_key']),axis=1)

    df['nickname_'] = df.apply(lambda row: decode_with_pycryptodome(row['nickname_'], row['secret_key']),axis=1)
    df['signature_'] = df.apply(lambda row: decode_with_pycryptodome(row['signature_'], row['secret_key']),axis=1)
    df['categories_'] = df.apply(lambda row: decode_with_pycryptodome(row['categories_'], row['secret_key']),axis=1)
    
    # 3. 清理中间列（可选）
    cols_to_drop = [f'xml{i}_' for i in range(1, 7)]
    df = df.drop(columns=[col for col in cols_to_drop if col in df.columns])
    return df

from py_mini_racer import MiniRacer

def decode_csv_with_js(df):
    # 初始化 JavaScript 环境（只初始化一次）
    ctx = MiniRacer()
    js_code = ""
    # 读取 JavaScript 代码
    # with open("./index_xml.global.js", "r", encoding='utf-8') as file:
    project_root = Path(__file__).parent.parent  # 根据实际文件位置调整.parent次数    
    # 构建完整的JS文件路径（跨平台安全）
    js_path = project_root / "utils" / "index_xml.global.js"
    if not js_path.exists():
        print(f"JS文件未找到: {js_path}")
        raise FileNotFoundError(f"JS文件未找到: {js_path}")
    
    with open(js_path, "r", encoding='utf-8') as file:
        js_code = file.read()
    ctx.eval(js_code)
    # 使用 apply 应用 JavaScript 函数
    def decode_func(x):
        try:
            return ctx.call("__WX_MER_DECODE__.decodeReportData.toXML", x) if pd.notna(x) else None
        except Exception as e:
            print(f"Error occurred for value: {x}")  # 打印报错的值
            print(e)
            return None
    
    tqdm.pandas(desc="Decoding CSV")
    df['decoded'] = df['combined'].progress_apply(decode_func)
    # df['decoded'] = df['combined'].apply(
    #     lambda x: ctx.call("__WX_MER_DECODE__.decodeReportData.toXML", x) if pd.notna(x) else None
    # )
    
    return df

def new_process_dataframe(df):
    tqdm.pandas(desc="Processing rows")
    df[['decoded', 'clickitem_']] = df.progress_apply(process_row, axis=1)
    return df

def process_row(row):
    native_info = row['nativeinfo_']
    decoded = row['decoded']
    if native_info and len(row.get('clickitem_', None)) == 0:
        # native_info is sth like '<native><tab-bar id=xxx>...</tab-bar></native>'
        new_html = f"""
        <page>
            <iframe>
                {decoded}
            </iframe>
            {native_info}
        </page>
        """
        return pd.Series([new_html, row.get('clickitem_', None)], index=['decoded', 'clickitem_'])
    return pd.Series([decoded, row.get('clickitem_', None)], index=['decoded', 'clickitem_'])

from datetime import datetime, timedelta
def convert_time_string(time_str: str) -> tuple[str, str, str]:
    # 添加年份并解析输入的时间字符串
    current_time = datetime.strptime(f"2025-{time_str}", "%Y-%m-%d %H:%M:%S.%f")
    
    # 计算前后2分钟的时间
    two_mins_before = current_time - timedelta(minutes=5)
    two_mins_after = current_time + timedelta(minutes=5)
    
    # 格式化输出
    current_formatted = current_time.strftime("%Y-%m-%d %H:%M:%S")
    before_formatted = two_mins_before.strftime("%Y-%m-%d %H:%M:%S")
    after_formatted = two_mins_after.strftime("%Y-%m-%d %H:%M:%S")
    
    return before_formatted, current_formatted, after_formatted

# 使用示例
# time_str = "04-29 21:01:24.794"
# before, current, after = convert_time_string(time_str)
# print(f"两分钟前: {before}")
# print(f"当前时间: {current}")
# print(f"两分钟后: {after}")

def make_sql_test(event_time, sessionid):
    # 先打印列信息（调试时使用）
    debug_sql = "SELECT * FROM dw_luban.log_34771 LIMIT 0"
    debug_df = rawins.query_dataframe(debug_sql)
    print("调试信息 - 所有列名:", debug_df.columns.tolist())
    
    # 原查询逻辑
    start_time, _, end_time = convert_time_string(event_time)
    sql = f"""
    SELECT * FROM dw_luban.log_34771 
    WHERE sessionid_ = '{sessionid}'
    AND hour_ BETWEEN toDateTime('{start_time}') AND toDateTime('{end_time}')
    """
    return sql

def make_sql(event_time, sessionid):
    # safe_sql = "SELECT * FROM dw_luban.log_34771 LIMIT 0"
    # # 执行后通过df.columns获取列名
    # try:
    #     columns_info = rawins.query_dataframe(safe_sql)
    #     print("列信息:\n", columns_info)
    # except Exception as e:
    #     print("查询失败:", str(e))
    
    start_time, _, end_time = convert_time_string(event_time)
    # sql = f"""
    # select * from dw_luban.log_34771  where sessionid_ in ('{sessionid}') 
    # AND hour_ <= toDateTime('{end_time}') 
    # AND hour_ >= toDateTime('{start_time}')
    # """
    # 别像上面这样写, 感觉时间有问题
    sql = f"""
    select * from dw_luban.log_34771  where sessionid_ in ('{sessionid}') 
    """
    return sql
# sessionid_ in ('hash=2850304358&ts=1745924472499&host=&version=671103613&device=2#241952638#1745924482869#CGI3', 'hash=2850304358&ts=1745924472499&host=&version=671103613&device=2#213099465#1745924477125#CGI3')

class ErrorType(Enum):
    EMPTY_DATA = auto()
    NONE = auto()
def get_sessionid_log_v2(app_id_,userid_,start_time,end_time):
    start_timestamp = int(start_time.timestamp()*1000)
    end_timestamp = int(end_time.timestamp()*1000)
    print("start_time",start_time)
    print("end_time",end_time)
    print("start_timestamp",start_timestamp)
    print("end_timestamp",end_timestamp)

    sql = f"""
        SELECT * FROM dw_luban.log_34771 
        WHERE appid_ = '{app_id_}'
        AND userid_ = '{userid_}'
        AND (CAST(splitByString('#', sessionid_)[3] AS UInt64)) >= {start_timestamp}
        AND (CAST(splitByString('#', sessionid_)[3] AS UInt64)) <= {end_timestamp}
        """
    print("sql",sql)

    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    def process_row_(row):
        row = pd.DataFrame([row])
        row = process_dataframe(row)
        row = row[row[['combined', 'clickitem_', 'nativeinfo_']].notna().all(axis=1)]
        row = row[['appid_', 'sessionid_', 'clickitem_', 'length_', 'nativeinfo_', 'combined', 'nickname_', 'signature_', 'categories_', 'path_', 'bitmapurl_','userid_','lastsessionid_','starttime_','endtime_']]
        
        row = decode_csv_with_js(row)
        
        # print(row.shape)
        # print('df.columns', row.columns)
        row = row[row['decoded'].notna()]
        row = row[['appid_', 'sessionid_', 'clickitem_', 'length_', 'nativeinfo_', 'combined', 'decoded', 'nickname_', 'signature_', 'categories_', 'path_', 'bitmapurl_','userid_','lastsessionid_','starttime_','endtime_']]
        row = row[row.length_ < 1024*6]
        
        row = new_process_dataframe(row)
        row['short_sessionid_'] = row['sessionid_'].apply(get_session_id)
        row['timestamp_'] = row['sessionid_'].apply(get_timestamp)
        return row.iloc[0] if not row.empty else pd.Series()

    processed_rows = []
    for _, row in df.iterrows():
        try:
            processed_row = process_row_(row)
            if not processed_row.empty:
                processed_rows.append(processed_row)
        except Exception as e:
            print(f"处理行数据时出错: {e}")
    
    # 合并处理后的数据
    processed_df = pd.DataFrame(processed_rows)
    print("处理后的数据形状:", processed_df.shape)
    return processed_df

def get_sessionid_log(event_time, sessionid, save=False, sql=None):
    if sql is None:
        sql = make_sql(event_time, sessionid)
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    print("####################", type(df))
    print("####################df.shape:", df.shape)
    if df.shape[0] == 0:
        return ErrorType.EMPTY_DATA
    # df = df[:100]
    print('df.shape', df.shape)
    print('df.columns', df.columns)
    df = process_dataframe(df)
    print(df.shape)

    df = df[df[['combined', 'clickitem_', 'nativeinfo_']].notna().all(axis=1)]
    print('filter None: ', df.shape)
    # df = df[['appid_', 'sessionid_', 'clickitem_', 'length_', 'nativeinfo_', 'combined', 'nickname_', 'signature_', 'categories_', 'path_', 'bitmapurl_']]
    df = df[['appid_', 'sessionid_', 'clickitem_', 'length_', 'nativeinfo_', 'combined', 'nickname_', 'signature_', 'categories_', 'path_', 'bitmapurl_','userid_','lastsessionid_','starttime_','endtime_']]
    df = decode_csv_with_js(df)
    df = df[df[['decoded']].notna().all(axis=1)]
    print('after decoded:', df.shape)
    print('====='*5)
    # print(df['clickitem_'])
    print('====='*5)
    # print(df['combined'])
    print('====='*5)
    #print(df['decoded'])
    # df = df[['appid_', 'sessionid_', 'clickitem_', 'length_', 'nativeinfo_', 'decoded',  'nickname_', 'signature_', 'categories_', 'path_', 'bitmapurl_']]
    df = df[['appid_', 'sessionid_', 'clickitem_', 'length_', 'nativeinfo_', 'combined', 'nickname_', 'signature_', 'categories_', 'path_', 'bitmapurl_','userid_','lastsessionid_','starttime_','endtime_']]
    df = df[df.length_ < 1024*6]
    print('filter length', df.shape)

    df = new_process_dataframe(df)
    print(df.shape)
    print('final df.columns', df.columns)
    if save:
        df.to_pickle('mianbao_test.pickle')
    return df

# main('04-29 21:01:24.794', 'hash=2850304358&ts=1745931650999&host=&version=671103613&device=2#191463280#1745931684794#CGI3')
if __name__ == "__main__":
    df = get_sessionid_log(None, None, save=True, sql="""select * from dw_luban.log_34771  where hour_ >= toDateTime('2025-05-17 00:00:00') 
  and appid_ in ('wxcf62686dc9d61f90', 'wx729238547ac7a14c', 'wx56dbc76c7b340f86', 
                  'wx366ae41607c228d1', 'wxc1d4e8f52a93da7e', 'wxad3150031786d672', 
                  'wx4ffb369b6881ee5e', 'wxc30ae3bc7fb4cab1', 'wxe671ce744efccefd', 
                  'wx565957244cb7d81f', 'wx3dcca19d0aa51755', 'wx8a5d6f9fad07544e', 
                  'wx734c1ad7b3562129')
  and sessionid_ LIKE '%#CGI5%'""")
    print(df[:100])