import base64
import gzip
from Crypto.Cipher import AES
from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA

def decode_with_pycryptodome(encoded_str: str, secret_key: bytes) -> str:
    try:
        if len(secret_key) not in [16, 24, 32]:
            print(
                f"无效的AES密钥长度: {len(secret_key)}字节，"
                "应为16/24/32字节（对应AES-128/192/256）"
            )
            return None
    except Exception as e:
        return None
    
    try:
        combined_data = base64.b64decode(encoded_str)
        
        iv = combined_data[:12]  # 前12字节为IV
        ciphertext_with_tag = combined_data[12:]  # 剩余为密文+标签
        ciphertext = ciphertext_with_tag[:-16]  # 倒数16字节为标签
        tag = ciphertext_with_tag[-16:]
    except Exception as e:
        return None
    
    try:
        cipher = AES.new(secret_key, AES.MODE_GCM, nonce=iv)
        compressed_data = cipher.decrypt_and_verify(ciphertext, tag)
    except ValueError as e:
        print("解密失败（认证标签无效或数据损坏）")
        return None
    
    original_bytes = gzip.decompress(compressed_data)
    
    return original_bytes.decode('utf-8').strip()

def load_private_key_from_base64() -> RSA.RsaKey:
    pem_cleaned = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCZWvQqegCbv6tfoYb1qjZ3z5qAViVc41nmNc7NrFlZUKZiEMG4Qo7Iel7xVH7LjRH1xzsanhsBnWJNEY/O4emG7rU4EJIPzXEB5VlNFCrR/givbDG/f5h51AS3KT05eJJQqCwBzgpJ/mUIAawjeXf4Y059PZQyrBUi/mOm0koAX8pPbuOkf8RUirv6/rmJOtMakp+fVfRUfmZ+VL53Mee8zReMb/q0CBogfgVte8Ab/adxR4D8qkFEdEupMBu5RwWGu3LWcDaYNaBx2bjR5M2ItaR9RfVqMW8gNWnsaRskrmNlrxcHO9JmFqA4qdJr8HWK6Phj2+SILiJ5MI6FWj9tAgMBAAECggEAP0XnuB7eYWSTsMFUjcfM8WIWvRzODaVAUluhF/skYlWEULHZO806XD0hLzFNn251GpM5L6JMQj5NcL7uNs2/LI0Q+DDeEucQ2VdnoQXRJRNLQhXB8OEUaC+yQmAhgrTcqaq5fYUleaIczVDjVT+o4DMZD1N48e9OSUHsm8foAH7hQtrRfd1lZ3zpZPTWgvY2IoIApvCj0K6j6pTqKVfiWOzMU4W79gyLhGO+xA0XLx/NzRklkEy5iav4s4gDZzbhRKvDRMSqez+0i1DWf1EgieCJ4E3bG51pZHTnKWMDI1PojJBcYNt6bC3Xw7R7LVzQ+cuEF/3SFkwf7aSkITYIAQKBgQDXqufqQ0KwQV/GJ2YM9ITw0o/DC1FVuYWAL+0usCsORsvtp1GwBusQiI0UQKTGJzMekveVz1LDS5UFRDyuimm6tg0DABiNyrve4pw9HAtoqygF66aDJcbWcWpXe9qs4jvULYrtzvSkwJ8F4jE43bp7e+3zyOFzYMMH0LNVKJXGfQKBgQC2CNf5cvjhvM19OzmWiuU193+mkSCxYFOXmei6gtd6LD4CNFKOlDC3B0kSutCozJU+N+wRkDxvhWSN5bOWi1InaT/IJQlaCMJRDrwaTsVmbXLFpHwLK/KT0Vi/JGrdsDQJnC9JK6GBML/4/2fym53oRdlo2fm9nK4miFUQteR/sQKBgQCDeRyIIxi0eiX4lmwP2cBxTut066FZsQ9BZfYhUCPvQBlxd17FAHKsUZIvlnjck2kGanGkUg8SHzFpVH/pp1dtCy5DdfuB9OUyqtK0LjSGSlDLyyxnJtBgSiOZ0VctrXzrjBf1gQSCqypGW+3FzOWyAYq94ugNfh9m1EN+1o73wQKBgF7qZZLP/C3YQF1YEEMK0VE+Z/T2H1HdpDicK9l0d95gS0Ti+1kUtAH7boKxnjMl2XvDtPfDeX+80h2a8/U/V+rs2jEF/sZPYuLZCAO/pvYtg3bCuVsp4yZoNhoUZxSI19la8tz4PxIZ1T+EDTNXKRL/uMkPP0U6+SNmWtSHw0lBAoGAdtip9qAX3ROHuFp6cz9huKXzfygqzef3i1RL3IgDaVHRiuIghHF3IpwCXztGtPi3+jjTUYoE4XI6Iu0CP5hvtx/gCXFCp51SA6cQJp4jlGGf9zcXJGXVDqAOOi1/vLtkXY7Kj0wHIXg9+d43/zTTlgSee8Y/dwvtKgRQYG/TKzk=".strip()
    # pem_cleaned = pem_cleaned.replace("-----BEGIN PRIVATE KEY-----", "")
    # pem_cleaned = pem_cleaned.replace("-----END PRIVATE KEY-----", "")
    # pem_cleaned = pem_cleaned.replace(" ", "").replace("\n", "")  # 关键：移除所有空格和换行
    
    key_bytes = base64.b64decode(pem_cleaned)
    
    # 打印调试信息
    # print("======= 调试信息 =======")
    # print(f"[DEBUG] 解码后的私钥字节长度: {len(key_bytes)}")
    # print("[DEBUG] key_bytes (HEX):")
    # print(key_bytes.hex())  # 以 HEX 格式输出

    key = RSA.import_key(key_bytes)
    
    return key

def decrypt_with_rsa(private_key: RSA.RsaKey, encrypted_data_b64: str) -> bytes:
    try:
        encrypted_bytes = base64.b64decode(encrypted_data_b64)   
        #print(f"[DEBUG] 加密数据字节长度: {len(encrypted_bytes)}")  # 预期 256
        cipher = PKCS1_v1_5.new(private_key)
        decrypted_bytes = cipher.decrypt(encrypted_bytes, sentinel=None)
    except Exception as e:
        print(e)
        return None

    if decrypted_bytes is None:
        print("解密失败（密钥不匹配或数据损坏）")
        return None
    return decrypted_bytes


def replace_consecutive_stars(s: str) -> str:
    if not s:
        return s
    
    result = []
    count = 0
    
    # 遍历字符串中的每个字符
    for i, char in enumerate(s):
        if char == '*':
            count += 1
            # 如果是最后一个字符且星号数量大于等于3
            if i == len(s) - 1 and count >= 3:
                result.extend(['0'] * (len(result) - count + 1))
        else:
            # 如果之前有连续3个及以上的星号，替换为'0'
            if count >= 3:
                result.extend(['0'] * (len(result) - count + 1))
            else:
                # 否则保留原有的星号
                result.extend(['*'] * count)
            result.append(char)
            count = 0
    
    # 处理字符串末尾的星号
    if count > 0:
        if count >= 3:
            result.append('0')
        else:
            result.extend(['*'] * count)
    
    return ''.join(result)


