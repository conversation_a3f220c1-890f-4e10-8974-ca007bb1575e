import json
import math
import time
import pandas as pd
import requests
import re
from fastapi import APIRouter, Body, Query, HTTPException, Path, Depends, Header, Request, status
from ..utils.xml_decode_test import ErrorType, get_sessionid_log
from ..utils.group_by_session_id import get_session_id, get_timestamp
from ..utils.batch_simplify_xml import process_trajectory
from ..models.response import StandardResponse, ErrorCode
from api.models.task import GetSessionRequest, GetSessionResponse
from api.services.session_service import SessionService
router = APIRouter()

def process_data(event_time, sessionid, delete_decoded=False):
    try:
        df = get_sessionid_log(event_time, sessionid, save=False)
        if not isinstance(df, pd.DataFrame):
            if df == ErrorType.EMPTY_DATA:
                return None
            else:
                assert False, "Unknown error"
        
        df['short_sessionid_'] = df['sessionid_'].apply(get_session_id)
        df['timestamp_'] = df['sessionid_'].apply(get_timestamp)
        trajectories = df.to_dict('records')
        results = []
        for trajectory in trajectories:
            simplied_dom = process_trajectory(trajectory)
            if delete_decoded:
                del simplied_dom['decoded']
            results.append(json.dumps(simplied_dom, ensure_ascii=False, indent=2))
        print(results)
        return results
    except Exception as e:
        raise


@router.get("/get_session_data", response_model=StandardResponse[list])
async def process_session(event_time: str = Query(...), sessionid: str = Query(...)):
    try:
        result = process_data(event_time, sessionid)
        if result is None:
            return StandardResponse(code=ErrorCode.NOT_FOUND, msg="No data found", data=[])
        return StandardResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# app_id,user_id,start_time,end_time
# appid wx21c7506e98a2fe75
# userid 2d172f8a07b572a1458b2adac8669183eff01e85f4f7ba5db7f3302cb00429bf
# 2025-05-27 12:57:10 -> timestamp
# 2025-05-27 12:58:10 -> timestamp
@router.post("/get_session_data", response_model=StandardResponse[GetSessionResponse])
async def process_session_for_annotation(request: GetSessionRequest):
    operations = await SessionService.get_operations(request)
    return StandardResponse(data=operations)   