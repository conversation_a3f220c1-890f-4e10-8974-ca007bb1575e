from fastapi import APIRouter
from typing import List, Optional
from api.models.task import (
    CreateTaskRequest,
    CreateTaskResponse,
    Task,
    AssignTaskRequest,
    StartTaskRequest,
    FinishTaskRequest,
    DeleteTaskRequest,
    HasPermissionRequest,
    HasPermissionResponse,
    PaginatedTasksResponse,
)
from api.services.annotation_task_service import AnnotationTaskService
from api.models.response import StandardResponse

router = APIRouter()

@router.post("/", response_model=StandardResponse[CreateTaskResponse])
async def create_task(request: CreateTaskRequest):
    task_id = await AnnotationTaskService.create_task(request)
    return StandardResponse(data=CreateTaskResponse(task_id=task_id))

@router.get("/", response_model=StandardResponse[PaginatedTasksResponse])
async def list_tasks(
    assignee: Optional[str] = None,
    app_id: Optional[str] = None,
    status: Optional[str] = None,
    is_deleted: Optional[bool] = None,
    is_backfilled: Optional[bool] = None,
    page: int = 1,
    page_size: int = 10
):
    result = await AnnotationTaskService.list_tasks(
        assignee=assignee,
        app_id=app_id,
        status=status,
        is_deleted=is_deleted,
        is_backfilled=is_backfilled,
        page=page,
        page_size=page_size
    )
    return StandardResponse(data=result)

@router.get("/{task_id}", response_model=StandardResponse[Task])
async def get_task(task_id: int):
    task = await AnnotationTaskService.get_task(task_id)
    return StandardResponse(data=task)

@router.post("/assign", response_model=StandardResponse[bool])
async def assign_tasks(request: AssignTaskRequest):
    success = await AnnotationTaskService.assign_tasks(request)
    return StandardResponse(data=success)

@router.post("/start", response_model=StandardResponse[bool])
async def start_task(request: StartTaskRequest):
    success = await AnnotationTaskService.start_task(request)
    return StandardResponse(data=success)

@router.post("/finish", response_model=StandardResponse[bool])
async def finish_task(request: FinishTaskRequest):
    success = await AnnotationTaskService.finish_task(request)
    return StandardResponse(data=success)

@router.post("/delete", response_model=StandardResponse[bool])
async def delete_task(request: DeleteTaskRequest):
    success = await AnnotationTaskService.delete_task(request)
    return StandardResponse(data=success)

@router.post("/admin", response_model=StandardResponse[HasPermissionResponse])
async def has_permission(request: HasPermissionRequest):
    has_permission = await AnnotationTaskService.has_permission(request)
    return StandardResponse(data=HasPermissionResponse(has_permission=has_permission)) 