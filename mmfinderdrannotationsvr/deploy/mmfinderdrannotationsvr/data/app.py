from fastapi import Fast<PERSON><PERSON>, HTTPException

from fastapi.exceptions import RequestValidationError
from api.controllers import session, annotation_tasks
from api.middleware.error_handler import validation_exception_handler, http_exception_handler, general_exception_handler
from api.models.response import StandardResponse
from contextlib import asynccontextmanager
from api.controllers import session
from api.controllers import annotation_tasks
from api.db.database import init_db, close_db

@asynccontextmanager
async def lifespan(app: FastAPI):
    await init_db("postgresql+asyncpg://admin:Wxassistant_20141213@21.87.161.10/brightfu")
    yield
    await close_db()

app = FastAPI(lifespan=lifespan)

# 注册异常处理器
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

app.include_router(session.router, prefix="/session")
app.include_router(annotation_tasks.router, prefix="/annotations/tasks")

@app.get("/health")
async def health():
    return StandardResponse(data="OK")

if __name__ == "__main__":
    import uvicorn
    import sys
    uvicorn.run(app, host="0.0.0.0", port=int(sys.argv[1]))